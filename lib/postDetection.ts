// 帖子检测工具库 - 使用结构聚类方法检测网页中的帖子列表

/**
 * DOM元素的结构签名
 */
interface ElementSignature {
  tagName: string
  classNames: string[]
  childrenCount: number
  textLength: number
  linkCount: number
  imageCount: number
  tagSequence: string // 标签名称序列，如 "div>h2>a>span"
}

/**
 * 检测到的帖子元素信息
 */
interface DetectedPost {
  element: Element
  signature: ElementSignature
  title?: string
  link?: string
  preview?: string
}

/**
 * 结构化的帖子信息JSON对象
 */
export interface PostInfo {
  title: string
  xpath: string
  author: string
  time: string
  link: string
}

/**
 * 聚类结果
 */
interface ClusterResult {
  signature: ElementSignature
  elements: Element[]
  confidence: number // 置信度 (0-1)
  posts: DetectedPost[]
}

/**
 * 转义XPath中的特殊字符
 */
function escapeXPathString(str: string): string {
  // 如果字符串中没有单引号，用单引号包围
  if (!str.includes("'")) {
    return `'${str}'`
  }
  // 如果字符串中没有双引号，用双引号包围
  if (!str.includes('"')) {
    return `"${str}"`
  }
  // 如果都有，使用concat函数
  const parts = str.split("'").map(part => `'${part}'`)
  return `concat(${parts.join(", \"'\", ")})`
}

/**
 * 验证XPath是否能正确定位到指定元素
 */
function validateXPath(xpath: string, targetElement: Element): boolean {
  try {
    const result = document.evaluate(
      xpath,
      document,
      null,
      XPathResult.FIRST_ORDERED_NODE_TYPE,
      null
    )
    return result.singleNodeValue === targetElement
  } catch (error) {
    console.warn(`[XPath验证] XPath "${xpath}" 验证失败:`, error)
    return false
  }
}

/**
 * 生成元素的XPath路径
 */
function getElementXPath(element: Element): string {
  // 优先使用ID属性（如果存在且有效）
  if (element.id && element.id.trim()) {
    const escapedId = escapeXPathString(element.id)
    const idXPath = `//*[@id=${escapedId}]`
    if (validateXPath(idXPath, element)) {
      return idXPath
    }
  }

  // 检查是否有唯一的class属性
  if (element.className && element.className.trim()) {
    const classes = element.className.trim().split(/\s+/).filter(cls => cls.length > 0)
    if (classes.length > 0) {
      // 尝试使用第一个类名生成较短的路径
      const firstClass = classes[0]
      const classXPath = `//*[@class='${firstClass}']`
      if (validateXPath(classXPath, element)) {
        return classXPath
      }
      
      // 尝试使用contains方式匹配class
      const containsClassXPath = `//*[contains(@class, '${firstClass}')]`
      const containsElements = document.evaluate(
        containsClassXPath,
        document,
        null,
        XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
        null
      )
      if (containsElements.snapshotLength === 1 && containsElements.snapshotItem(0) === element) {
        return containsClassXPath
      }
    }
  }

  const parts: string[] = []
  let currentElement = element as Element | null

  while (currentElement && currentElement.nodeType === Node.ELEMENT_NODE) {
    // 停止在body或html标签，避免路径过长
    if (currentElement.tagName.toLowerCase() === 'html') {
      break
    }

    let tagName = currentElement.tagName.toLowerCase()
    
    // 如果有ID，在这里停止并使用ID
    if (currentElement.id && currentElement.id.trim() && currentElement !== element) {
      const escapedId = escapeXPathString(currentElement.id)
      parts.unshift(`//*[@id=${escapedId}]`)
      break
    }

    if (currentElement.parentNode && currentElement.parentNode.nodeType === Node.ELEMENT_NODE) {
      const parent = currentElement.parentNode as Element
      const siblings = Array.from(parent.children)
      const sameTagSiblings = siblings.filter(sibling => 
        sibling.tagName.toLowerCase() === tagName
      )
      
      if (sameTagSiblings.length > 1) {
        // 使用更可靠的索引计算方法
        let index = 1
        for (let i = 0; i < siblings.length; i++) {
          if (siblings[i] === currentElement) {
            break
          }
          if (siblings[i].tagName.toLowerCase() === tagName) {
            index++
          }
        }
        tagName += `[${index}]`
      }
    }
    
    parts.unshift(tagName)
    currentElement = currentElement.parentElement

    // 限制路径深度，避免过长
    if (parts.length > 10) {
      break
    }
  }

  // 如果路径为空，返回通用路径
  if (parts.length === 0) {
    return '//*'
  }

  // 确保路径以/开头
  let xpath = parts[0].startsWith('//*') ? parts.join('/') : '/' + parts.join('/')
  
  // 验证生成的XPath是否正确
  if (validateXPath(xpath, element)) {
    return xpath
  }

  // 如果基本路径验证失败，尝试使用更具体的属性
  console.warn(`[XPath生成] 基本XPath验证失败: ${xpath}`)
  
  // 尝试添加文本内容作为辅助定位
  const textContent = element.textContent?.trim()
  if (textContent && textContent.length > 0 && textContent.length < 50) {
    const shortText = textContent.substring(0, 20).replace(/'/g, '"') // 简单转义
    const textXPath = `//*[contains(text(), '${shortText}')]`
    if (validateXPath(textXPath, element)) {
      return textXPath
    }
  }

  // 尝试使用简单的位置信息
  const tagName = element.tagName.toLowerCase()
  const parent = element.parentElement
  if (parent && parent.tagName.toLowerCase() !== 'html') {
    const siblings = Array.from(parent.children).filter(child => 
      child.tagName.toLowerCase() === tagName
    )
    if (siblings.length > 1) {
      const index = siblings.indexOf(element) + 1
      const parentTag = parent.tagName.toLowerCase()
      // 使用简单的父子关系，避免递归
      const positionXPath = `//${parentTag}/${tagName}[${index}]`
      if (validateXPath(positionXPath, element)) {
        return positionXPath
      }
    }
  }

  // 如果所有方法都失败，返回基本路径并记录警告
  console.warn(`[XPath生成] 无法生成可靠的XPath，返回基本路径: ${xpath}`)
  return xpath
}

/**
 * 提取DOM元素的结构签名
 */
function extractElementSignature(element: Element): ElementSignature {
  // 获取标签名序列（深度限制为3层）
  function getTagSequence(el: Element, depth = 0, maxDepth = 3): string {
    if (depth >= maxDepth) return el.tagName.toLowerCase()
    
    const children = Array.from(el.children)
    if (children.length === 0) return el.tagName.toLowerCase()
    
    // 取前3个子元素的标签序列
    const childSequences = children
      .slice(0, 3)
      .map(child => getTagSequence(child, depth + 1, maxDepth))
      .join(',')
    
    return `${el.tagName.toLowerCase()}>${childSequences}`
  }

  const classNames = Array.from(element.classList).filter(cls => cls.trim() !== '')
  const textLength = element.textContent?.trim().length || 0
  const linkCount = element.querySelectorAll('a').length
  const imageCount = element.querySelectorAll('img').length
  const childrenCount = element.children.length

  return {
    tagName: element.tagName.toLowerCase(),
    classNames,
    childrenCount,
    textLength,
    linkCount,
    imageCount,
    tagSequence: getTagSequence(element)
  }
}

/**
 * 计算两个结构签名的相似度
 */
function calculateSimilarity(sig1: ElementSignature, sig2: ElementSignature): number {
  let score = 0
  let totalWeights = 0

  // 标签名相似度（权重：0.3）
  const tagWeight = 0.3
  if (sig1.tagName === sig2.tagName) {
    score += tagWeight
  }
  totalWeights += tagWeight

  // 标签序列相似度（权重：0.25）
  const sequenceWeight = 0.25
  if (sig1.tagSequence === sig2.tagSequence) {
    score += sequenceWeight
  } else {
    // 计算序列的部分匹配度
    const seq1Parts = sig1.tagSequence.split('>')
    const seq2Parts = sig2.tagSequence.split('>')
    const commonParts = seq1Parts.filter(part => seq2Parts.includes(part))
    const partialScore = commonParts.length / Math.max(seq1Parts.length, seq2Parts.length)
    score += sequenceWeight * partialScore
  }
  totalWeights += sequenceWeight

  // 类名相似度（权重：0.2）
  const classWeight = 0.2
  if (sig1.classNames.length > 0 && sig2.classNames.length > 0) {
    const commonClasses = sig1.classNames.filter(cls => sig2.classNames.includes(cls))
    const classScore = commonClasses.length / Math.max(sig1.classNames.length, sig2.classNames.length)
    score += classWeight * classScore
  }
  totalWeights += classWeight

  // 子元素数量相似度（权重：0.1）
  const childrenWeight = 0.1
  if (sig1.childrenCount > 0 && sig2.childrenCount > 0) {
    const childrenScore = 1 - Math.abs(sig1.childrenCount - sig2.childrenCount) / 
                         Math.max(sig1.childrenCount, sig2.childrenCount)
    score += childrenWeight * childrenScore
  }
  totalWeights += childrenWeight

  // 链接数量相似度（权重：0.1）
  const linkWeight = 0.1
  if (sig1.linkCount === sig2.linkCount) {
    score += linkWeight
  } else if (sig1.linkCount > 0 && sig2.linkCount > 0) {
    const linkScore = 1 - Math.abs(sig1.linkCount - sig2.linkCount) / 
                     Math.max(sig1.linkCount, sig2.linkCount)
    score += linkWeight * linkScore
  }
  totalWeights += linkWeight

  // 图片数量相似度（权重：0.05）
  const imageWeight = 0.05
  if (sig1.imageCount === sig2.imageCount) {
    score += imageWeight
  }
  totalWeights += imageWeight

  return totalWeights > 0 ? score / totalWeights : 0
}

/**
 * 提取作者信息
 */
function extractAuthor(element: Element): string {
  const authorSelectors = [
    '[class*="author"]', '[class*="user"]', '[class*="by"]',
    '.author', '.user', '.username', '.by-author',
    '[data-author]', '[data-user]', '[data-username]',
    'a[href*="/user/"]', 'a[href*="/author/"]', 'a[href*="/u/"]',
    '.post-author', '.comment-author', '.article-author'
  ]

  for (const selector of authorSelectors) {
    const authorEl = element.querySelector(selector)
    if (authorEl && authorEl.textContent?.trim()) {
      return authorEl.textContent.trim()
    }
  }

  return ''
}

/**
 * 提取时间信息
 */
function extractTime(element: Element): string {
  const timeSelectors = [
    'time', '[datetime]', '[class*="time"]', '[class*="date"]',
    '.time', '.date', '.timestamp', '.published', '.created',
    '[data-time]', '[data-date]', '[data-timestamp]',
    '.post-time', '.comment-time', '.article-time'
  ]

  for (const selector of timeSelectors) {
    const timeEl = element.querySelector(selector)
    if (timeEl) {
      // 优先使用datetime属性
      const datetime = timeEl.getAttribute('datetime')
      if (datetime) return datetime
      
      // 否则使用文本内容
      const timeText = timeEl.textContent?.trim()
      if (timeText) return timeText
    }
  }

  // 尝试匹配时间格式的文本
  const allText = element.textContent || ''
  const timePatterns = [
    /\d{4}-\d{2}-\d{2}/g,  // 2023-12-01
    /\d{2}\/\d{2}\/\d{4}/g, // 12/01/2023
    /\d+\s*小时前/g,        // 2小时前
    /\d+\s*分钟前/g,        // 30分钟前
    /\d+\s*天前/g,          // 3天前
    /昨天|今天|前天/g        // 相对时间
  ]

  for (const pattern of timePatterns) {
    const matches = allText.match(pattern)
    if (matches && matches.length > 0) {
      return matches[0]
    }
  }

  return ''
}

/**
 * 将检测到的帖子转换为结构化JSON对象
 */
function convertToPostInfo(detectedPost: DetectedPost): PostInfo {
  const element = detectedPost.element
  
  return {
    title: detectedPost.title || '(无标题)',
    xpath: getElementXPath(element),
    author: extractAuthor(element) || '(无作者)',
    time: extractTime(element) || '(无时间)',
    link: detectedPost.link || '(无链接)'
  }
}

/**
 * 提取帖子信息
 */
function extractPostInfo(element: Element): DetectedPost {
  const signature = extractElementSignature(element)
  
  // 尝试提取标题
  let title = ''
  const titleSelectors = [
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    '[class*="title"]', '[class*="heading"]', 
    'a[href]', '.title', '.headline', '.post-title'
  ]
  
  for (const selector of titleSelectors) {
    const titleEl = element.querySelector(selector)
    if (titleEl && titleEl.textContent?.trim()) {
      title = titleEl.textContent.trim()
      break
    }
  }

  // 尝试提取链接
  let link = ''
  const linkEl = element.querySelector('a[href]')
  if (linkEl) {
    link = (linkEl as HTMLAnchorElement).href
  }

  // 尝试提取预览文本
  let preview = ''
  const textContent = element.textContent?.trim() || ''
  if (textContent.length > title.length) {
    preview = textContent.substring(0, 200).replace(title, '').trim()
  }

  return {
    element,
    signature,
    title,
    link,
    preview
  }
}

/**
 * 对DOM元素进行结构聚类
 */
function clusterElements(elements: Element[], minClusterSize = 3, similarityThreshold = 0.7): ClusterResult[] {
  console.log(`[帖子检测] 开始聚类分析，候选元素数量: ${elements.length}`)
  
  const signatures = elements.map(extractElementSignature)
  const clusters: ClusterResult[] = []
  const used = new Set<number>()

  for (let i = 0; i < elements.length; i++) {
    if (used.has(i)) continue

    const cluster = {
      signature: signatures[i],
      elements: [elements[i]],
      confidence: 0,
      posts: []
    }

    // 查找相似的元素
    for (let j = i + 1; j < elements.length; j++) {
      if (used.has(j)) continue

      const similarity = calculateSimilarity(signatures[i], signatures[j])
      console.log(`[帖子检测] 元素 ${i} 与元素 ${j} 相似度: ${similarity.toFixed(3)}`)

      if (similarity >= similarityThreshold) {
        cluster.elements.push(elements[j])
        used.add(j)
      }
    }

    // 只保留足够大的聚类
    if (cluster.elements.length >= minClusterSize) {
      used.add(i)
      
      // 计算置信度
      cluster.confidence = Math.min(cluster.elements.length / 10, 1)
      
      // 提取帖子信息
      cluster.posts = cluster.elements.map(extractPostInfo)
      
      clusters.push(cluster)
      console.log(`[帖子检测] 发现聚类: ${cluster.elements.length} 个元素，置信度: ${cluster.confidence.toFixed(3)}`)
    }
  }

  return clusters.sort((a, b) => b.confidence - a.confidence)
}

/**
 * 检测页面中的帖子列表
 */
export function detectPostLists(): ClusterResult[] {
  console.log('[帖子检测] 开始检测页面中的帖子列表...')
  
  // 排除的容器选择器
  const excludeSelectors = [
    'script', 'style', 'nav', 'footer', 'header', 
    '.navigation', '.sidebar', '.menu', '.ads', '.advertisement'
  ]

  // 候选容器选择器（可能包含帖子列表的容器）
  const candidateSelectors = [
    'article', '.post', '.item', '.entry', '.card', 
    '[class*="post"]', '[class*="item"]', '[class*="entry"]',
    '[class*="article"]', '[class*="card"]', '[class*="list"]',
    'li', '.row', '[class*="row"]', '.content', '[class*="content"]'
  ]

  const allCandidates: Element[] = []
  
  // 收集候选元素
  for (const selector of candidateSelectors) {
    try {
      const elements = document.querySelectorAll(selector)
      console.log(`[帖子检测] 选择器 "${selector}" 找到 ${elements.length} 个元素`)
      
      elements.forEach(el => {
        // 检查是否在排除列表中
        const shouldExclude = excludeSelectors.some(excludeSelector => {
          return el.matches(excludeSelector) || el.closest(excludeSelector)
        })
        
        if (!shouldExclude && el.textContent && el.textContent.trim().length > 20) {
          allCandidates.push(el)
        }
      })
    } catch (error) {
      console.warn(`[帖子检测] 选择器 "${selector}" 执行失败:`, error)
    }
  }

  // 去重（移除重复的元素）
  const uniqueCandidates = Array.from(new Set(allCandidates))
  console.log(`[帖子检测] 去重后的候选元素数量: ${uniqueCandidates.length}`)

  // 按元素大小过滤（太小的元素可能不是帖子）
  const filteredCandidates = uniqueCandidates.filter(el => {
    const rect = el.getBoundingClientRect()
    const hasContent = el.textContent && el.textContent.trim().length > 20
    const hasReasonableSize = rect.width > 100 && rect.height > 30
    
    return hasContent && hasReasonableSize
  })

  console.log(`[帖子检测] 尺寸过滤后的候选元素数量: ${filteredCandidates.length}`)

  // 进行聚类分析
  const clusters = clusterElements(filteredCandidates, 3, 0.6)
  
  console.log(`[帖子检测] 检测完成，发现 ${clusters.length} 个可能的帖子列表`)
  
  // 转换为结构化JSON对象并输出
  const allPostsJson: PostInfo[] = []
  
  clusters.forEach((cluster, index) => {
    console.group(`[帖子检测] 聚类 ${index + 1}`)
    console.log('元素数量:', cluster.elements.length)
    console.log('置信度:', cluster.confidence.toFixed(3))
    console.log('结构签名:', cluster.signature)
    
    // 转换当前聚类的帖子为JSON对象
    const clusterPostsJson = cluster.posts.map(convertToPostInfo)
    allPostsJson.push(...clusterPostsJson)
    
    console.log('帖子JSON对象列表:')
    clusterPostsJson.forEach((postJson, postIndex) => {
      console.log(`  帖子 ${postIndex + 1}:`, postJson)
    })
    console.groupEnd()
  })

  // 输出所有帖子的完整JSON数组
  if (allPostsJson.length > 0) {
    console.group('[帖子检测] 完整JSON数据')
    console.log('检测到的所有帖子JSON对象数组:')
    console.log(JSON.stringify(allPostsJson, null, 2))
    console.groupEnd()
    
    // 输出统计信息
    console.log(`[帖子检测] 数据统计: 共检测到 ${allPostsJson.length} 个帖子`)
    console.log(`[帖子检测] 有效标题: ${allPostsJson.filter(p => p.title !== '(无标题)').length} 个`)
    console.log(`[帖子检测] 有效作者: ${allPostsJson.filter(p => p.author !== '(无作者)').length} 个`)
    console.log(`[帖子检测] 有效时间: ${allPostsJson.filter(p => p.time !== '(无时间)').length} 个`)
    console.log(`[帖子检测] 有效链接: ${allPostsJson.filter(p => p.link !== '(无链接)').length} 个`)
  }

  return clusters
}

/**
 * 高亮显示检测到的帖子列表（用于调试）
 */
export function highlightDetectedPosts(clusters: ClusterResult[]) {
  // 移除之前的高亮
  document.querySelectorAll('[data-post-highlight]').forEach(el => {
    (el as HTMLElement).style.outline = ''
    el.removeAttribute('data-post-highlight')
  })

  // 添加新的高亮
  clusters.forEach((cluster, clusterIndex) => {
    const colors = ['red', 'blue', 'green', 'orange', 'purple']
    const color = colors[clusterIndex % colors.length]
    
    cluster.elements.forEach(el => {
      (el as HTMLElement).style.outline = `2px solid ${color}`
      el.setAttribute('data-post-highlight', `cluster-${clusterIndex}`)
    })
  })

  console.log(`[帖子检测] 已高亮显示 ${clusters.length} 个聚类的帖子`)
}

/**
 * 获取检测到的帖子JSON数据
 */
export function getPostsAsJson(): PostInfo[] {
  const clusters = detectPostLists()
  const allPostsJson: PostInfo[] = []
  
  clusters.forEach(cluster => {
    const clusterPostsJson = cluster.posts.map(convertToPostInfo)
    allPostsJson.push(...clusterPostsJson)
  })
  
  return allPostsJson
} 

/**
 * 通过XPath定位元素并高亮显示
 * @param xpath 元素的XPath路径
 * @returns 是否成功定位到元素
 */
export function navigateToElementByXPath(xpath: string): boolean {
  try {
    // 尝试使用XPath查找元素
    const result = document.evaluate(
      xpath,
      document,
      null,
      XPathResult.FIRST_ORDERED_NODE_TYPE,
      null
    );
    
    const element = result.singleNodeValue as HTMLElement;
    
    // 如果找到元素
    if (element) {
      // 滚动到元素位置
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
      
      // 临时高亮显示元素
      const originalOutline = element.style.outline;
      const originalBoxShadow = element.style.boxShadow;
      const originalPosition = element.style.position;
      const originalZIndex = element.style.zIndex;
      
      element.style.outline = '3px solid #4CAF50';
      element.style.boxShadow = '0 0 10px rgba(76, 175, 80, 0.7)';
      element.style.position = 'relative';
      element.style.zIndex = '10000';
      
      // 添加脉动动画效果
      const pulseAnimation = document.createElement('style');
      pulseAnimation.textContent = `
        @keyframes pulseHighlight {
          0% { outline-color: rgba(76, 175, 80, 1); box-shadow: 0 0 10px rgba(76, 175, 80, 0.7); }
          50% { outline-color: rgba(76, 175, 80, 0.5); box-shadow: 0 0 15px rgba(76, 175, 80, 0.9); }
          100% { outline-color: rgba(76, 175, 80, 1); box-shadow: 0 0 10px rgba(76, 175, 80, 0.7); }
        }
      `;
      document.head.appendChild(pulseAnimation);
      
      element.style.animation = 'pulseHighlight 1s infinite';
      
      // 3秒后恢复原样
      setTimeout(() => {
        element.style.outline = originalOutline;
        element.style.boxShadow = originalBoxShadow;
        element.style.position = originalPosition;
        element.style.zIndex = originalZIndex;
        element.style.animation = '';
        document.head.removeChild(pulseAnimation);
      }, 3000);
      
      console.log('[帖子导航] 成功定位到元素:', element);
      return true;
    } else {
      console.warn('[帖子导航] 未找到匹配的元素:', xpath);
      return false;
    }
  } catch (error) {
    console.error('[帖子导航] 定位元素时出错:', error);
    return false;
  }
} 